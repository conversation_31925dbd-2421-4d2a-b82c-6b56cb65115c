<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import { computed, watch } from 'vue';

const props = defineProps({
    products: Array,
});

const form = useForm({
    product_id: '',
    quantity: 1,
    notes: '',
});

const selectedProduct = computed(() => {
    return props.products.find(p => p.id == form.product_id);
});

const totalPrice = computed(() => {
    if (selectedProduct.value && form.quantity) {
        return (parseFloat(selectedProduct.value.price) * parseInt(form.quantity)).toFixed(2);
    }
    return '0.00';
});

const maxQuantity = computed(() => {
    return selectedProduct.value ? selectedProduct.value.stock_quantity : 0;
});

// Ajustar quantidade se exceder o estoque
watch(() => form.product_id, () => {
    if (selectedProduct.value && form.quantity > selectedProduct.value.stock_quantity) {
        form.quantity = selectedProduct.value.stock_quantity;
    }
});

const submit = () => {
    form.post(route('sales.store'));
};
</script>

<template>
    <Head title="Nova Venda" />

    <AuthenticatedLayout>
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">
                Nova Venda
            </h2>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900">
                        <form @submit.prevent="submit" class="space-y-6">
                            <div>
                                <label for="product_id" class="block text-sm font-medium text-gray-700">Produto</label>
                                <select
                                    id="product_id"
                                    v-model="form.product_id"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    required
                                >
                                    <option value="">Selecione um produto</option>
                                    <option v-for="product in products" :key="product.id" :value="product.id">
                                        {{ product.name }} - R$ {{ parseFloat(product.price).toFixed(2) }} (Estoque: {{ product.stock_quantity }})
                                    </option>
                                </select>
                                <div v-if="form.errors.product_id" class="mt-2 text-sm text-red-600">
                                    {{ form.errors.product_id }}
                                </div>
                            </div>

                            <div v-if="selectedProduct" class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Detalhes do Produto</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm text-gray-600">Nome: <span class="font-medium">{{ selectedProduct.name }}</span></p>
                                        <p class="text-sm text-gray-600">SKU: <span class="font-medium">{{ selectedProduct.sku }}</span></p>
                                        <p class="text-sm text-gray-600">Preço unitário: <span class="font-medium">R$ {{ parseFloat(selectedProduct.price).toFixed(2) }}</span></p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600">Estoque disponível: <span class="font-medium">{{ selectedProduct.stock_quantity }}</span></p>
                                        <p class="text-sm text-gray-600">Categoria: <span class="font-medium">{{ selectedProduct.category || '-' }}</span></p>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label for="quantity" class="block text-sm font-medium text-gray-700">Quantidade</label>
                                <TextInput
                                    id="quantity"
                                    v-model="form.quantity"
                                    type="number"
                                    min="1"
                                    :max="maxQuantity"
                                    class="mt-1 block w-full"
                                    required
                                />
                                <div v-if="form.errors.quantity" class="mt-2 text-sm text-red-600">
                                    {{ form.errors.quantity }}
                                </div>
                                <div v-if="selectedProduct && form.quantity > selectedProduct.stock_quantity" class="mt-2 text-sm text-red-600">
                                    Quantidade não pode ser maior que o estoque disponível ({{ selectedProduct.stock_quantity }})
                                </div>
                            </div>

                            <div v-if="selectedProduct" class="bg-blue-50 p-4 rounded-lg">
                                <h3 class="text-lg font-medium text-blue-900 mb-2">Resumo da Venda</h3>
                                <div class="space-y-1">
                                    <p class="text-sm text-blue-800">Produto: <span class="font-medium">{{ selectedProduct.name }}</span></p>
                                    <p class="text-sm text-blue-800">Quantidade: <span class="font-medium">{{ form.quantity }}</span></p>
                                    <p class="text-sm text-blue-800">Preço unitário: <span class="font-medium">R$ {{ parseFloat(selectedProduct.price).toFixed(2) }}</span></p>
                                    <p class="text-lg font-bold text-blue-900">Total: R$ {{ totalPrice }}</p>
                                </div>
                            </div>

                            <div>
                                <label for="notes" class="block text-sm font-medium text-gray-700">Observações</label>
                                <textarea
                                    id="notes"
                                    v-model="form.notes"
                                    rows="3"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    placeholder="Observações sobre a venda (opcional)"
                                ></textarea>
                                <div v-if="form.errors.notes" class="mt-2 text-sm text-red-600">
                                    {{ form.errors.notes }}
                                </div>
                            </div>

                            <div class="flex items-center justify-end space-x-4">
                                <SecondaryButton @click="$inertia.visit(route('sales.index'))">
                                    Cancelar
                                </SecondaryButton>
                                <PrimaryButton :disabled="form.processing || !selectedProduct || form.quantity > maxQuantity">
                                    Realizar Venda
                                </PrimaryButton>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
