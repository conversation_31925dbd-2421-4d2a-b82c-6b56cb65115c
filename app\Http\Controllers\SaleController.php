<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\Product;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SaleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): Response
    {
        $sales = Sale::with(['product', 'user'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('Sales/Index', [
            'sales' => $sales
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        $products = Product::active()->inStock()->get();

        return Inertia::render('Sales/Create', [
            'products' => $products
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string',
        ]);

        $product = Product::findOrFail($validated['product_id']);

        // Verificar se há estoque suficiente
        if ($product->stock_quantity < $validated['quantity']) {
            return back()->withErrors([
                'quantity' => 'Estoque insuficiente. Disponível: ' . $product->stock_quantity
            ]);
        }

        DB::transaction(function () use ($validated, $product) {
            // Criar a venda
            Sale::create([
                'user_id' => Auth::id(),
                'product_id' => $validated['product_id'],
                'quantity' => $validated['quantity'],
                'unit_price' => $product->price,
                'total_price' => $product->price * $validated['quantity'],
                'status' => 'completed',
                'notes' => $validated['notes'] ?? null,
                'sale_date' => now(),
            ]);

            // Atualizar estoque
            $product->decrement('stock_quantity', $validated['quantity']);
        });

        return redirect()->route('sales.index')
            ->with('success', 'Venda realizada com sucesso!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Sale $sale): Response
    {
        return Inertia::render('Sales/Show', [
            'sale' => $sale->load(['product', 'user'])
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Sale $sale): Response
    {
        $products = Product::active()->get();

        return Inertia::render('Sales/Edit', [
            'sale' => $sale->load('product'),
            'products' => $products
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Sale $sale)
    {
        $validated = $request->validate([
            'quantity' => 'required|integer|min:1',
            'status' => 'required|in:pending,completed,cancelled',
            'notes' => 'nullable|string',
        ]);

        $sale->update([
            'quantity' => $validated['quantity'],
            'total_price' => $sale->unit_price * $validated['quantity'],
            'status' => $validated['status'],
            'notes' => $validated['notes'] ?? null,
        ]);

        return redirect()->route('sales.index')
            ->with('success', 'Venda atualizada com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Sale $sale)
    {
        // Restaurar estoque se a venda foi completada
        if ($sale->status === 'completed') {
            $sale->product->increment('stock_quantity', $sale->quantity);
        }

        $sale->delete();

        return redirect()->route('sales.index')
            ->with('success', 'Venda removida com sucesso!');
    }
}
