version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: laravel_app
    volumes:
      - ./:/var/www
    ports:
      - "9000:9000"
      - "5173:5173"
    networks:
      - laravel

  webserver:
    image: nginx:alpine
    container_name: nginx_webserver_app
    ports:
      - "8000:80"
    volumes:
      - ./:/var/www
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - app
    networks:
      - laravel

  db:
    image: mysql:8.0
    container_name: mysql_db_app
    environment:
      MYSQL_DATABASE: ${DB_DATABASE:-laravel}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD:-root}
      MYSQL_PASSWORD: ${DB_PASSWORD:-userpass}
      MYSQL_USER: ${DB_USERNAME:-username}
    ports:
      - "3308:3306"
    volumes:
      - dbdata:/var/lib/mysql
    networks:
      - laravel

networks:
  laravel:
    driver: bridge

volumes:
  dbdata: