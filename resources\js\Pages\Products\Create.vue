<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

const form = useForm({
    name: '',
    description: '',
    price: '',
    stock_quantity: '',
    sku: '',
    category: '',
    image_url: '',
});

const submit = () => {
    form.post(route('products.store'));
};
</script>

<template>
    <Head title="Novo Produto" />

    <AuthenticatedLayout>
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">
                Novo Produto
            </h2>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900">
                        <form @submit.prevent="submit" class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700">Nome do Produto</label>
                                    <TextInput
                                        id="name"
                                        v-model="form.name"
                                        type="text"
                                        class="mt-1 block w-full"
                                        required
                                        autofocus
                                    />
                                    <div v-if="form.errors.name" class="mt-2 text-sm text-red-600">
                                        {{ form.errors.name }}
                                    </div>
                                </div>

                                <div>
                                    <label for="sku" class="block text-sm font-medium text-gray-700">SKU</label>
                                    <TextInput
                                        id="sku"
                                        v-model="form.sku"
                                        type="text"
                                        class="mt-1 block w-full"
                                        required
                                    />
                                    <div v-if="form.errors.sku" class="mt-2 text-sm text-red-600">
                                        {{ form.errors.sku }}
                                    </div>
                                </div>

                                <div>
                                    <label for="price" class="block text-sm font-medium text-gray-700">Preço</label>
                                    <TextInput
                                        id="price"
                                        v-model="form.price"
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        class="mt-1 block w-full"
                                        required
                                    />
                                    <div v-if="form.errors.price" class="mt-2 text-sm text-red-600">
                                        {{ form.errors.price }}
                                    </div>
                                </div>

                                <div>
                                    <label for="stock_quantity" class="block text-sm font-medium text-gray-700">Quantidade em Estoque</label>
                                    <TextInput
                                        id="stock_quantity"
                                        v-model="form.stock_quantity"
                                        type="number"
                                        min="0"
                                        class="mt-1 block w-full"
                                        required
                                    />
                                    <div v-if="form.errors.stock_quantity" class="mt-2 text-sm text-red-600">
                                        {{ form.errors.stock_quantity }}
                                    </div>
                                </div>

                                <div>
                                    <label for="category" class="block text-sm font-medium text-gray-700">Categoria</label>
                                    <TextInput
                                        id="category"
                                        v-model="form.category"
                                        type="text"
                                        class="mt-1 block w-full"
                                    />
                                    <div v-if="form.errors.category" class="mt-2 text-sm text-red-600">
                                        {{ form.errors.category }}
                                    </div>
                                </div>

                                <div>
                                    <label for="image_url" class="block text-sm font-medium text-gray-700">URL da Imagem</label>
                                    <TextInput
                                        id="image_url"
                                        v-model="form.image_url"
                                        type="url"
                                        class="mt-1 block w-full"
                                    />
                                    <div v-if="form.errors.image_url" class="mt-2 text-sm text-red-600">
                                        {{ form.errors.image_url }}
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700">Descrição</label>
                                <textarea
                                    id="description"
                                    v-model="form.description"
                                    rows="3"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                ></textarea>
                                <div v-if="form.errors.description" class="mt-2 text-sm text-red-600">
                                    {{ form.errors.description }}
                                </div>
                            </div>

                            <div class="flex items-center justify-end space-x-4">
                                <SecondaryButton @click="$inertia.visit(route('products.index'))">
                                    Cancelar
                                </SecondaryButton>
                                <PrimaryButton :disabled="form.processing">
                                    Criar Produto
                                </PrimaryButton>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
